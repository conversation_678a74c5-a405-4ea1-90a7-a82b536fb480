import { Component ,HostListener , inject} from '@angular/core';
import { AuthServices } from '../../../services/auth/auth.services';
import { RouterLink } from '@angular/router';
import Notiflix from 'notiflix';
@Component({
  selector: 'app-navbar',
  imports: [RouterLink],
  templateUrl: './navbar.html',
  styleUrl: './navbar.scss'
})
export class Navbar {
  authService: AuthServices = inject(AuthServices);
  dropdownOpen = false;
  user: any;
  constructor(){
    this.user = this.authService.getUserData();
  }

  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  handleSettings(event: MouseEvent) {
    event.preventDefault();
    alert('Settings clicked! Replace this with your settings logic.');
    this.dropdownOpen = false;
  }

  handleLogout(event: MouseEvent) {
    event.preventDefault();
    this.authService.logout();
    this.dropdownOpen = false;
    Notiflix.Notify.success('Logout successfuly');
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.user-menu')) {
      this.dropdownOpen = false;
    }
  }
}
