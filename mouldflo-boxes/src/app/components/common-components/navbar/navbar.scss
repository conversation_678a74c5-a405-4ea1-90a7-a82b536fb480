.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;              /* Full width */
    background: white;
    padding: 15px 0px;
    border-radius: 0;         /* Remove rounded corners if spanning full width */
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;            /* Keep it on top of other content */
}


.logo {
    font-size: 28px;
    padding-left: 40px;
    font-weight: bold;
    color: #2c5aa0;
}
.avatar-logo {
    height: 30px;
    width: 30px;
    // padding-right: 30px;
    border-radius: 50%; /* makes it circular */
    object-fit: cover;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.2s ease;
  
    &:hover {
      transform: scale(1.05);
    }
  }

.logo span {
    color: #4a90e2;
}
.logo-section{
  cursor: pointer;
}

  .user-menu {
    position: relative;
    display: inline-block;
    padding-right: 40px;
  }
  
  
  .dropdown-content {
    display: none;
    position: absolute;
    right: 20px;
    top: 100%;
    background-color: white;
    min-width: 180px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    z-index: 1000;
    // margin-top: 8px;
    border: 1px solid #e0e0e0;
    animation: fadeIn 0.2s ease-in-out;
  }
  
  .dropdown-content.show {
    display: block;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
  
    &:last-child {
      border-bottom: none;
    }
  
    &:hover {
      background-color: #f8f9fa;
    }
  
    &:first-child {
      border-radius: 8px 8px 0 0;
    }
  
    &:last-child {
      border-radius: 0 0 8px 8px;
    }
  
    &:first-child:last-child {
      border-radius: 8px;
    }
  }
  
  .dropdown-icon {
    width: 16px !important;
    height: 16px;
    margin-right: 12px;
    opacity: 0.7;
  }
  
  .logout-item {
    color: #dc3545;
  
    &:hover {
      background-color: #fef2f2;
    }
  }

  .dropdown-icon-main{
    padding-top: 3px;
    width: 10px;
  }

.user-section{
  display: flex;
  align-items: center;
   gap: 8px;
  cursor: pointer;
}