* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
}

.header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.back-btn {
    background: #ffffff;
    color: rgb(67, 124, 248);
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;

}

// .back-btn:hover {
//     background: #f7f8f8;
//     transform: translateY(-1px);
// }

.container {
    // max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    height: 90vh;
}

.box-header {
    // background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    // color: white;
    // padding: 1rem 2rem 2rem 2rem;
    // border-radius: 15px;
    // margin-bottom: 2rem;
    // box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);


    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #5084d6;
    color: white;
    padding: 1rem 2rem 2rem 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.box-title {
    font-size: 2rem;
    // margin-bottom: 0.5rem;
    margin: 0.5rem 0rem 0rem 0rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-running { background: #10b981; }
.status-stopped { background: #ef4444; }
.status-paused { background: #f59e0b; }
.version { background: #edf0ef; color: #5084D6; letter-spacing: 0.5px;}

.box-subtitle {
    opacity: 0.9;
    font-size: 1.1rem;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.card-title {
    font-size: 1.2rem;
    color: #4a90e2;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f1f5f9;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.info-label {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    color: #1e293b;
    font-weight: 500;
    text-transform: capitalize;
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.action-btn {
    padding: 0.8rem 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.btn-primary {
    background: #4a90e2;
    color: white;
}

.btn-primary:hover {
    background: #357abd;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f1f5f9;
    color: #4a90e2;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #e2e8f0;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.metric {
    text-align: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #4a90e2;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a90e2;
}

.metric-label {
    font-size: 0.8rem;
    color: #64748b;
    margin-top: 0.3rem;
}

.logs-container {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    max-height: 48vh;
    overflow-y: auto;
}

.log-line {
    margin: 0.2rem 0;
    padding: 0.2rem 0;
}

.timestamp {
    color: #64748b;
}

@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 1rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .box-title {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

.connection-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    animation: pulse 2s infinite;
}

.running { background: #10b981; }
.paused { background: #f59e0b; }
.stopped{ background: #ef4444; }
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}


// .box-header {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     background: #5084d6;
//     color: white;
//     padding: 16px 24px;
//     border-radius: 12px;
//   }
  
  .box-left {
    flex-shrink: 0;
  }
  
  .back-btn {
    background-color: white;
    color: #5084d6;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: bold;
    cursor: pointer;
  }
  
  .box-center {
    flex-grow: 1;
    padding: 0 2rem;
  }
  
  .box-title {
    font-size: 24px;
    font-weight: bold;
  }
  

  .container-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  
    th, td {
      padding: 10px 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
      font-size: 1rem;
      color: #1e293b;
      font-weight: 500;
    }
  
    th {
      color: #64748b;
      background-color: #f9fafb;
      font-weight: 600;
      font-size: 0.85rem;
      text-transform: uppercase;
    }
  
    tr:hover {
      background-color: #f3f4f6;
    }
  }
  