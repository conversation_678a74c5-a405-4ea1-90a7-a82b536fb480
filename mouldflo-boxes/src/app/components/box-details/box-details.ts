import { Component ,signal ,inject} from '@angular/core';
import { CommonModule } from '@angular/common'; 
import { RouterLink,ActivatedRoute ,Router} from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AuthServices } from '../../services/auth/auth.services';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { BoxServices } from '../../services/boxes/boxes';
import Notiflix from 'notiflix';
@Component({
  selector: 'app-box-details',
  imports: [CommonModule,RouterLink,NgxSkeletonLoaderModule,FormsModule],
  templateUrl: './box-details.html',
  styleUrl: './box-details.scss'
})


export class BoxDetails {
  authService: AuthServices = inject(AuthServices);
  boxService: BoxServices = inject(BoxServices);
  route: ActivatedRoute = inject(ActivatedRoute);
  router: Router = inject(Router);

  boxData = signal<any>({});
  deviceInfo = signal<any>({});
  performanceMetrics = signal<any>({});
  containersInfo = signal<any[]>([]);
  networkInfo = signal<any[]>([]);
  enviornmentVariables = signal<any>({});
  originalOrder = () => 0;
  loading = signal(true);
  currentBoxId:any;
  // logs = [
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  // ];

  actions = [
    { label: 'Update', icon: '▶' },
    // { label: 'Pause', icon: '⏸' },
    { label: 'Restart', icon: '🔄' },
    // { label: 'Configure', icon: '⚙' },
    { label: 'Stop', icon: '⏹' }
  ];


  ngOnInit(): void {
    this.route.paramMap.subscribe((params:any) => {
      const boxId = params.get('id');
      this.currentBoxId = boxId;
      if(boxId){
        this.getBoxDetails(boxId)
      }
    });

  }


  handleAction(action: string): void {
    alert(`Action triggered: ${action}`);
  }


    getBoxDetails(id:string){
      this.loading.set(false)
      console.log('this.boxService.boxDetails() :', this.boxService.boxDetails());
      if(this.boxService.boxDetails()){
        const response:any = this.boxService.boxDetails()
        console.log('response :', response);
        this.containersInfo.set(response?.docker_ps);
        // this.networkInfo.set(response?.box_details?.network_info)
        if(response?._env){
          let data ={
            "SSH_PORT": response?._env?.SSH_PORT,
            "MAC_ADDRESS": response?._env?.MAC_ADDRESS,
            "HOST_IP": response?._env?.HOST_IP,
            "IMAGE_TAG": response?._env?.IMAGE_TAG
          }
          this.deviceInfo.set(data);
        }
        if(response?.apt_info){
          let data ={
            "box_id" : this.currentBoxId,
            "Host_IP": response?._env?.HOST_IP,
            "version" : response?.apt_info?.Version,
            "status" : "running",
          }
          this.boxData.set(data);
        }
        if(response?.df){
          let avlData = response?.df.find((res:any)=>res.Filesystem == '/dev/root')
          console.log('avlData :', avlData);
          let data={
            "Use" : avlData?.['Use%'],
            "Available": avlData?.['Available']
          }
          this.performanceMetrics.set(data);
        }
        if(response?.envData){
          let data = {
            "SSH_PORT": response?.envData?.SSH_PORT,
            "MAC_ADDRESS": response?.envData?.MAC_ADDRESS,
            "HOST_IP": response?.envData?.HOST_IP,
            "IMAGE_TAG": response?.envData?.IMAGE_TAG
          }
          this.enviornmentVariables.set(data);
        }
      }else{
        this.collectBoxData()
      }
  }

  collectBoxData(){
    this.loading.set(true)
    this.boxService.getBoxDetails(this.currentBoxId,'').subscribe(
      (response) => {
        this.loading.set(false)
        this.boxService.boxDetails.set(response?.data);
        this.getBoxDetails(this.currentBoxId)
      },
      (error) => {
        this.loading.set(false)
        console.log(error);
        if(error.status === 401 ){
          this.router.navigate(['/dashboard']);
        }
        if(error.status === 404 ){
          Notiflix.Notify.failure(error?.error.detail || 'Box not found!');
          this.router.navigate(['/dashboard']);
        }
      }
    );
  }




}
