import { Component ,signal ,inject} from '@angular/core';
import { CommonModule } from '@angular/common'; 
import { RouterLink,ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AuthServices } from '../../services/auth/auth.services';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { BoxServices } from '../../services/boxes/boxes';
@Component({
  selector: 'app-box-details',
  imports: [CommonModule,RouterLink,NgxSkeletonLoaderModule,FormsModule],
  templateUrl: './box-details.html',
  styleUrl: './box-details.scss'
})


export class BoxDetails {
  authService: AuthServices = inject(AuthServices);
  boxService: BoxServices = inject(BoxServices);
  route: ActivatedRoute = inject(ActivatedRoute);


  boxData = signal<any>({});
  deviceInfo = signal<any>({});
  performanceMetrics = signal<any>({});
  containersInfo = signal<any[]>([]);
  networkInfo = signal<any[]>([]);
  enviornmentVariables = signal<any>({});
  originalOrder = () => 0;
  loading = signal(true);
  currentBoxId:any;
  // logs = [
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  //   { time: '14:32:15', message: 'System started successfully' },
  //   { time: '14:32:20', message: 'Connection established with server' },
  //   { time: '14:32:25', message: 'Sensor calibration completed' },
  //   { time: '14:32:30', message: 'Production cycle initiated' },
  //   { time: '14:32:35', message: 'Temperature stabilized at 72°C' },
  //   { time: '14:32:40', message: 'Quality check passed' },
  //   { time: '14:32:45', message: 'Output rate: 150 units/hour' },
  // ];

  actions = [
    { label: 'Update', icon: '▶' },
    // { label: 'Pause', icon: '⏸' },
    { label: 'Restart', icon: '🔄' },
    // { label: 'Configure', icon: '⚙' },
    { label: 'Stop', icon: '⏹' }
  ];


  ngOnInit(): void {
    this.route.paramMap.subscribe((params:any) => {
      const boxId = params.get('id');
      this.currentBoxId = boxId;
      if(boxId){
        this.getBoxDetails(boxId)
      }
    });

  }


  handleAction(action: string): void {
    alert(`Action triggered: ${action}`);
  }

  // getBoxDetails(id: string): void {
  //   this.authService.getBoxDetails(id,this.boxPassword).subscribe(
  //     (response) => {
        
  //     },
  //     (error) => {
  //       console.log(error);
  //     }
  //   );


    getBoxDetails(id:string){
      this.loading.set(false)
      console.log('this.boxService.boxDetails() :', this.boxService.boxDetails());
      if(this.boxService.boxDetails()){
        const response:any = this.boxService.boxDetails()
        console.log('response :', response);
        this.containersInfo.set(response?.box_details?.docker_ps);
        // this.networkInfo.set(response?.box_details?.network_info)
        if(response?.box_details?._env){
          let data ={
            "SSH_PORT": response?.box_details?._env?.SSH_PORT,
            "MAC_ADDRESS": response?.box_details?._env?.MAC_ADDRESS,
            "HOST_IP": response?.box_details?._env?.HOST_IP,
            "IMAGE_TAG": response?.box_details?._env?.IMAGE_TAG
          }
          this.deviceInfo.set(data);
        }
        if(response?.box_details?.apt_info){
          let data ={
            "box_id" : this.currentBoxId,
            "Host_IP": response?.box_details?._env?.HOST_IP,
            "version" : response?.box_details?.apt_info?.Version,
            "status" : "running",
          }
          this.boxData.set(data);
        }
        if(response?.box_details?.df){
          let avlData = response?.box_details?.df.find((res:any)=>res.Filesystem == '/dev/root')
          console.log('avlData :', avlData);
          let data={
            "Use" : avlData?.['Use%'],
            "Available": avlData?.['Available']
          }
          this.performanceMetrics.set(data);
        }
        if(response?.box_details?.envData){
          let data = {
            "SSH_PORT": response?.box_details?.envData?.SSH_PORT,
            "MAC_ADDRESS": response?.box_details?.envData?.MAC_ADDRESS,
            "HOST_IP": response?.box_details?.envData?.HOST_IP,
            "IMAGE_TAG": response?.box_details?.envData?.IMAGE_TAG
          }
          this.enviornmentVariables.set(data);
        }
      } 
  }




}
