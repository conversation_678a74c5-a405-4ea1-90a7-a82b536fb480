<div class="container">
  
  <div class="box-header">
      <div class="box-left">
        <button class="back-btn" routerLink="/dashboard">&larr; Back</button>
      </div>
        <div class="box-center">
          <div class="box-title">
            <p style="font-size: larger; min-width: 180px;">MOULD-{{ boxData().box_id }}</p>
            <span class="status-badge version">Version : {{ boxData().version }}</span>
            <span class="status-badge" [ngClass]="'status-' + boxData()?.status?.toLowerCase()">{{ boxData().status }}</span>
            <span class="connection-indicator" [ngClass]="boxData()?.status?.toLowerCase()"></span>
          </div>
          <div class="box-subtitle">
            IP : {{ boxData().Host_IP }} 
          </div>
        </div>
      
        <div class="box-right">
          <img src="images/box-logo.svg" alt="Mouldflo Logo" height="40" />
        </div>
      </div>
      
  
    <div class="content-grid">
      <div class="main-content">
        <div class="card">
          <div class="card-title">Device Information</div>
          <div class="info-grid">
            @if(!loading()){
              @for (info of deviceInfo() | keyvalue : originalOrder; track info ) {
                <div class="info-item">
                  <div class="info-label">{{ info.key}}</div>
                  <div class="info-value">{{ info.value }}</div>
                </div>
              }
            } @else{
              @for (info of [1,2,3,4] | keyvalue : originalOrder; track info ) {
                <div class="info-item">
                  <ngx-skeleton-loader appearance="line" />
                  <ngx-skeleton-loader appearance="line" />
                </div>
              }
            }
          </div>
        </div>
        <div class="card">
          <div class="card-title">Containers Information</div>
          <div class="info-grid">
            @if(loading()){
              <ngx-skeleton-loader count="5" appearance="line" />
            } @else{
              <table class="container-table">
                <thead>
                  <tr>
                    @for (container of containersInfo()[0] | keyvalue : originalOrder; track container) {
                      <th>{{ container.key}}</th>
                    }
                  </tr>
                </thead>
                <tbody>
                  @for (container of containersInfo(); track container) {
                    <tr>
                      @for (data of container | keyvalue : originalOrder; track data) {
                        <td>{{data.value}}</td>
                      }
                    </tr>
                  }
                </tbody>
              </table>
            }
          </div>
        </div>
        <div class="card">
            <div class="card-title">Enviornment Variables</div>
            @if(loading()){
              <ngx-skeleton-loader count="2" appearance="line" />
            } @else{
              <div class="info-grid">
                @for (info of enviornmentVariables() | keyvalue : originalOrder; track info){
                    <div class="info-item" >
                      <div class="info-label">{{ info.key }}</div>
                      <div class="info-value">{{ info.value }}</div>
                    </div>
                }
              </div>
            }
          </div>
      </div>
  
      <div class="sidebar">
        <div class="card">
          <div class="card-title">Performance Metrics</div>
          <div class="metrics-grid">
            @if(loading()){
              @for (metric of [1,2] | keyvalue : originalOrder; track metric){
                <div class="metric">
                  <ngx-skeleton-loader [theme]="{height: '40px'}"/>
                </div>
            }
            } @else{
                @for (metric of performanceMetrics() | keyvalue : originalOrder; track metric){
                    <div class="metric">
                      <div class="metric-value">{{ metric.value }}</div>
                      <div class="metric-label">{{ metric.key }}</div>
                    </div>
                }
            }

          </div>
        </div>
        <div class="card">
          <div class="card-title">Quick Actions</div>
          <div class="quick-actions" >
            @for (action of actions; track action){
                <button class="action-btn btn-primary" [disabled]="loading()" (click)="handleAction(action.label)">
                  {{ action.icon }} {{ action.label }}
                </button>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
