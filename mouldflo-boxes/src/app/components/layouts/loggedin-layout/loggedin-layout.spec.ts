import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LoggedinLayout } from './loggedin-layout';

describe('LoggedinLayout', () => {
  let component: LoggedinLayout;
  let fixture: ComponentFixture<LoggedinLayout>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LoggedinLayout]
    })
    .compileComponents();

    fixture = TestBed.createComponent(LoggedinLayout);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
