/* Ensure full height layout */
html, body {
    margin: 0;
    box-sizing: border-box;
  }
  
  .main-wrapper {
    height: calc(100vh - 60px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 20px;
  }
  
  .content {
    flex: 1;
    padding-top: 60px;
    padding-bottom: 40px;
    height: calc(100vh - 120px);
  }
  

  
  
  /* Footer style */
  app-footer {
    background-color: #f6f9fa;
    text-align: center;
    padding: 3px 0;
    font-size: 13px;
    color: #333;
    border-top: 1px solid #ddd;
  }
  