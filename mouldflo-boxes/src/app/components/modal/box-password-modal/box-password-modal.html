@if(isOpen){
    <div class="modal-overlay" >
      <div class="modal-box" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h2>Enter Box Password</h2>
          <button class="close-btn" (click)="closeModal()" [disabled]="isLoading">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
    
        <div class="modal-info">
          <div class="box-info-card">
              <svg class="box-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <div class="box-details">
                <span class="box-label">Selected Box</span>
                <span class="box-id">{{ selectedBoxId }}</span>
              </div>
          </div>
        </div>
    
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">Password</label>
            <div class="password-input-wrapper">
              <input
                class="password-input"
                [type]="showPassword ? 'text' : 'password'"
                [(ngModel)]="password"
                placeholder="Enter password"
                [disabled]="isLoading"
                (keydown.enter)="!isLoading && password.trim() && onSubmit()"
              />
              <button 
                type="button" 
                class="password-toggle" 
                (click)="showPassword = !showPassword" 
                [disabled]="isLoading"
              >
              @if(!showPassword){
                  <svg  width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
              }
              @if(showPassword){
                <svg  width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                  </svg>
            }
              </button>
            </div>
          </div>
        </div>
  
        <div class="modal-footer">
          <button class="btn btn-secondary" (click)="closeModal()" [disabled]="isLoading">
            Cancel
          </button>
          <button 
            class="btn btn-primary" 
            (click)="onSubmit()" 
            [disabled]="isLoading || !password.trim()"
          >
            @if(isLoading){
              <div class="spinner"></div>
            }
            @if(!isLoading){
                <svg  width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                  <circle cx="12" cy="16" r="1"></circle>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg>
              }
            {{ isLoading ? 'Submitting...' : 'Submit' }}
          </button>
        </div>
      </div>
    </div>
  }