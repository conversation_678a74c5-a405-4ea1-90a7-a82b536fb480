import { Component, EventEmitter, Input, Output, OnChanges, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-box-password-modal',
  standalone: true,
  imports: [FormsModule],
  templateUrl: './box-password-modal.html',
  styleUrl: './box-password-modal.scss'
})
export class BoxPasswordModal implements OnChanges {
  @Input() isOpen = false;
  @Input() selectedBoxId: string = '';
  @Output() submitPassword = new EventEmitter<{ boxId: string, password: string }>();
  @Output() cancel = new EventEmitter<void>();

  password = '';
  showPassword = false;
  isLoading = false;
  
  ngOnChanges(changes: SimpleChanges) {
    // Reset state when modal is closed
    if (changes['isOpen'] && !changes['isOpen'].currentValue) {
      this.resetState();
    }
  }

  onSubmit() {
    if (!this.password.trim()) return;
    this.isLoading = true;
    this.submitPassword.emit({ boxId: this.selectedBoxId, password: this.password });
  }

  closeModal() {
    this.resetState();
    this.cancel.emit();
  }
  
  private resetState() {
    this.password = '';
    this.showPassword = false;
    this.isLoading = false;
  }
}