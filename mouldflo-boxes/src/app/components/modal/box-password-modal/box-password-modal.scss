.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
  }
  
  .modal-box {
    background: white;
    border-radius: 16px;
    width: 100%;
    max-width: 480px;
    margin: 20px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    animation: slideIn 0.3s ease-out;
    overflow: hidden;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }
  
  .close-btn:hover:not(:disabled) {
    background: #f3f4f6;
    color: #374151;
  }
  
  .close-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .modal-info {
    padding: 20px 24px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .box-info-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #448AD7;
    border-radius: 12px;
    border: 2px solid #dbeafe;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    justify-content: space-between;
  }
  .box-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .box-icon {
    color: #3b82f6;
    flex-shrink: 0;
  }
  
  .box-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  .box-label {
    font-size: 12px;
    font-weight: 500;
    color: #ffff;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .box-id {
    font-size: 16px;
    font-weight: 600;
    color:#ffff;
  }
  
  .modal-body {
    padding: 24px;
  }
  
  .form-group {
    margin-bottom: 0;
  }
  
  .form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }
  
  .password-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .password-input {
    width: 100%;
    padding: 12px 16px;
    padding-right: 48px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    outline: none;
  }
  
  .password-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .password-input:disabled {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #6b7280;
    cursor: not-allowed;
  }
  
  .password-input::placeholder {
    color: #9ca3af;
  }
  
  .password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }
  
  .password-toggle:hover:not(:disabled) {
    color: #374151;
    background: #f3f4f6;
  }
  
  .password-toggle:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .modal-footer {
    display: flex;
    gap: 12px;
    padding: 20px 24px 24px;
    justify-content: flex-end;
  }
  
  .btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 100px;
    justify-content: center;
  }
  
  .btn-secondary {
    background: #f3f4f6;
    color: #374151;
  }
  
  .btn-secondary:hover:not(:disabled) {
    background: #e5e7eb;
  }
  
  .btn-primary {
    background: #3b82f6;
    color: white;
  }
  
  .btn-primary:hover:not(:disabled) {
    background: #2563eb;
  }
  
  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  /* Responsive */
  @media (max-width: 640px) {
    .modal-box {
      margin: 10px;
      border-radius: 12px;
    }
    
    .modal-header {
      padding: 20px 20px 16px;
    }
    
    .modal-info {
      padding: 16px 20px;
    }
    
    .modal-body {
      padding: 20px;
    }
    
    .modal-footer {
      padding: 16px 20px 20px;
      flex-direction: column;
    }
    
    .btn {
      width: 100%;
      order: 1;
    }
    
    .btn-secondary {
      order: 2;
    }
  }