
.search-container {
    position: relative;
    display: flex;
    justify-content: flex-end;  /* Pushes content to the right */
    align-items: center;
    padding: 10px;
    margin-top: 13px;
    margin-right: 25px;
    justify-content: space-between;
    margin-left: 15px;
}

.search-box {
    padding: 10px 40px 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    width: 400px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s;
    margin-right: 15px;
}

.search-box:focus {
    border-color: #4a90e2;
}

.search-icon {
    position: absolute;
    // right: calc(50% - 140px);
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none; /* so clicks go through to input */
    color: #999;
    font-size: 18px;
    margin-left: 420px;
  }

.dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    width: 99%;
    max-width: 100%;
    padding: 10px;
    height: calc(100vh - 180px);
    overflow: scroll;
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    padding: 10px;
    justify-content: flex-start;
  }
  @media screen and (max-width: 1024px) {
    .dashboard-grid {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
  }
  
  @media screen and (max-width: 768px) {
    .dashboard-grid {
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }
  }
  

.card-wrapper {
    position: relative;
    margin: 5px;
    padding: 5px;
}

.box-id-outer {
    font-size: 12px;
    color: #111111;
    margin-bottom: 8px;
    font-weight: 500;
    margin-left: 15px;
}

.mould-card {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border-radius: 15px;
    padding: 15px;
    color: white;
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
    position: relative;
    min-height: 260px;
}

.mould-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(74, 144, 226, 0.4);
}

.card-header {
    display: none;
}

.top-left-section {
    position: absolute;
    left: 20px;
    top: 15px;
}

.top-right-section {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.left-section {
    position: absolute;
    left: 20px;
    top: 60px;
}

.led-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.led-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.led-label {
    font-size: 12px;
    font-weight: 500;
    opacity: 0.9;
    min-width: 60px;
}

.middle-section {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.right-section {
    position: absolute;
    right: 40px;
    bottom: 40px;
    width: 200px;
    height: 140px;
}

.led {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
}

.led.running {
    background: #00ff00;
    box-shadow: 0 0 15px #00ff00, inset 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse-green 1s infinite;
}

.led.paused {
    background: #ffaa00;
    box-shadow: 0 0 15px #ffaa00, inset 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse-orange 1.5s infinite;
}

.led.stopped {
    background: #ff0000;
    box-shadow: 0 0 15px #ff0000, inset 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse-red 1s infinite;
}

@keyframes pulse-green {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

@keyframes pulse-orange {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes pulse-red {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.brand-title {
    font-size: 48px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    margin: 0;
    white-space: nowrap;
}

.info-card {
    background: rgba(255,255,255,0.95);
    border-radius: 8px;
    padding: 12px;
    color: #333;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    width: 100%;
    height: 98%;
    position: relative;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 13px;
    line-height: 1.3;
}

.info-label {
    font-weight: 600;
    color: #2c5aa0;
}

.info-value {
    color: #666;
}

.logo-small {
    position: absolute;
    bottom: 8px;
    right: 10px;
    font-size: 14px;
    font-weight: bold;
    color: #2c5aa0;
    opacity: 0.8;
}

.controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    z-index: 1000;
}

.control-btn {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 8px 12px;
    margin: 0 5px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s;
}

.control-btn:hover {
    background: #357abd;
}

.top-right-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
  }
  
  .title {
    font-size: 25px;
    font-weight: bold;
  }
  
  .subtitle {
    font-size: 18px;
    color: #fafafa;
    margin-top: 2px;
  }
  

  .dashboard-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    padding: 10px;
    justify-content: flex-start;
  }
  
  .card-wrapper {
    flex: 1 1 calc(25% - 50px); /* 4 cards per row */
    max-width: calc(25% - 25px);
    min-width: 260px;
    box-sizing: border-box;
  }
  
  /* Medium screen - 3 cards per row */
  @media screen and (max-width: 1200px) {
    .card-wrapper {
      flex: 1 1 calc(33.333% - 25px);
      max-width: calc(33.333% - 25px);
    }
  }
  
  /* Smaller screen - 2 cards per row */
  @media screen and (max-width: 900px) {
    .card-wrapper {
      flex: 1 1 calc(50% - 25px);
      max-width: calc(50% - 25px);
    }
  }
  
  /* Smallest screen - 1 card per row */
  @media screen and (max-width: 600px) {
    .card-wrapper {
      flex: 1 1 100%;
      max-width: 100%;
    }
  }
  

  .refresh-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 19px;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
    margin-right: 10px;
  }
  
  .refresh-btn:hover {
    background-color: #448AD8;
    color: white;
  }
  
  .refresh-icon {
    width: 16px;
    height: 16px;
  }
  
  .spin {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
 
  .svg-wrapper .custom-svg {
    fill: #737373; /* Default color */
    cursor: pointer;
    transition: fill 0.3s ease;
  }
  
  .svg-wrapper:hover .custom-svg {
    fill: red; /* Hover color */
  }
  

.page-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.empty-state {
  text-align: center;
  margin-top: 40px;
}

.main-icon {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-icon svg {
  z-index: 1;
}

.status-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  background-color: red;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.alert-icon {
  width: 20px;
  height: 20px;
  background-color: red;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
