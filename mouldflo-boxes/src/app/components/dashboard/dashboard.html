
<div class="dashboard-container">
    <div class="search-container">
      <div style="display: flex;">
        <input type="text" class="search-box" placeholder="Search Here..."  [(ngModel)]="searchTerm" (keyup)="getfilteredBoxes()" />
        <div class="search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" x="2px" y="2px" width="20" height="20" viewBox="0,0,256,256"
          style="fill:#737373;">
          <g fill="#737373" fill-rule="nonzero" stroke="none" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="10" stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none" font-size="none" text-anchor="none" style="mix-blend-mode: normal"><g transform="scale(5.12,5.12)"><path d="M21,3c-9.37891,0 -17,7.62109 -17,17c0,9.37891 7.62109,17 17,17c3.71094,0 7.14063,-1.19531 9.9375,-3.21875l13.15625,13.125l2.8125,-2.8125l-13,-13.03125c2.55469,-2.97656 4.09375,-6.83984 4.09375,-11.0625c0,-9.37891 -7.62109,-17 -17,-17zM21,5c8.29688,0 15,6.70313 15,15c0,8.29688 -6.70312,15 -15,15c-8.29687,0 -15,-6.70312 -15,-15c0,-8.29687 6.70313,-15 15,-15z"></path></g></g>
          </svg>
        </div>
      </div>
      <div class="refresh-btn" (click)="getAllBoxes()">
        <span class="refresh-icon" [ngClass]="{ 'spin': loading() }">
          <div class="svg-wrapper">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="15"
              height="15"
              viewBox="0,0,256,256"
              class="custom-svg"
            >
              <g fill="currentColor" fill-rule="nonzero">
                <g transform="scale(8.53333,8.53333)">
                  <path d="M15,3c-2.9686,0 -5.69718,1.08344 -7.79297,2.875c-0.28605,0.22772 -0.42503,0.59339 -0.36245,0.95363c0.06258,0.36023 0.31676,0.6576 0.66286,0.77549c0.3461,0.1179 0.72895,0.03753 0.99842,-0.20959c1.74821,-1.49444 4.01074,-2.39453 6.49414,-2.39453c5.19656,0 9.45099,3.93793 9.95117,9h-2.95117l4,6l4,-6h-3.05078c-0.51129,-6.14834 -5.67138,-11 -11.94922,-11zM4,10l-4,6h3.05078c0.51129,6.14834 5.67138,11 11.94922,11c2.9686,0 5.69718,-1.08344 7.79297,-2.875c0.28605,-0.22772 0.42504,-0.59339 0.36245,-0.95363c-0.06258,-0.36023 -0.31676,-0.6576 -0.66286,-0.7755c-0.3461,-0.1179 -0.72895,-0.03753 -0.99842,0.20959c-1.74821,1.49444 -4.01074,2.39453 -6.49414,2.39453c-5.19656,0 -9.45099,-3.93793 -9.95117,-9h2.95117z"></path>
                </g>
              </g>
            </svg>
          </div>          
        </span>
        Refresh
      </div>
      </div>
      @if(!loading()){
        @if(filteredData().length > 0){
          <div class="dashboard-grid">
            @for (data of filteredData(); track data){
              <div class="card-wrapper">
                <div class="box-id-outer">BOX ID: MOULD-{{data?.box_id}}</div>
                <div class="mould-card">
                  <div class="top-left-section">
                    <div class="led-container">
                      <div class="led-row">
                        <div class="led" [ngClass]="{ 'running': data?.status == 'running' }"></div>
                        <span class="led-label">Running</span>
                      </div>
                      <div class="led-row">
                        <div class="led" [ngClass]="{ 'paused': data?.status == 'paused' }"></div>
                        <span class="led-label">Paused</span>
                      </div>
                      <div class="led-row">
                        <div class="led" [ngClass]="{ 'stopped': data?.status == 'stopped' }"></div>
                        <span class="led-label">Stopped</span>
                      </div>
                    </div>
                  </div>
            
                  <div class="top-right-section">
                    <img src="images/box-logo.svg" alt="Mouldflo Logo" height="40" />
                    <div class="subtitle">Connect</div>
                  </div>
            
            
                  <div class="right-section" (click)="getBoxDeatils(data)">
                    <div class="info-card">
                      <div class="info-row">
                        <span class="info-label">Mac ADD:</span>
                        <span class="info-value">{{ data?.mac || "68:7D:78:00:5D:1C" }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">Model:</span>
                        <span class="info-value">{{ data?.model || "MOL-150A"}}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">Status:</span>
                        <span class="info-value">{{ data?.status ||"running" }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">UP Time:</span>
                        <span class="info-value">{{ data?.cycle || "45 sec"}}</span>
                      </div>
                      <div class="logo-small">Mouldflo</div>
                    </div>
                  </div>
                </div>
              </div>
            }
          </div>
        } @else {
          <div class="page-container">
            <div class="empty-state">
              <div class="main-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="white" viewBox="0 0 24 24">
                  <path d="M9.172 12l-4.95-4.95 1.414-1.414L10.586 10.586l4.95-4.95 1.414 1.414L12 12l4.95 4.95-1.414 1.414L10.586 13.414l-4.95 4.95-1.414-1.414z" />
                </svg>
          
                <div class="status-indicator">
                  <div class="dot"></div>
                  <div class="alert-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 24 24">
                      <path d="M1 21h22L12 2 1 21zm13-3h-2v-2h2v2zm0-4h-2v-4h2v4z" />
                    </svg>
                  </div>
                </div>
              </div>
              <h1>No Mouldflo Boxes Available</h1>
              <p>We couldn't detect any active Mouldflo device boxes on your network.</p>
            </div>
          </div>
        }
      } @else{
        <div class="dashboard-grid">

          @for (data of [1,2,3,4,5,6,7,8]; track data){
              <div class="card-wrapper">
                <div class="box-id-outer">BOX ID: </div>
                <ngx-skeleton-loader  [theme]="{height: '250px'}"/>
              </div>
          }
        </div>

      }
      <app-box-password-modal
  [isOpen]="isModalOpen"
  [selectedBoxId]="selectedBoxId"
  (submitPassword)="handlePasswordSubmit($event)"
  (cancel)="closeModal()"
></app-box-password-modal>

</div>
