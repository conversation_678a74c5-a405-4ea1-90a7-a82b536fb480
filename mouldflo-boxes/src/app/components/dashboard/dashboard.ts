import { Component ,inject ,signal} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthServices } from '../../services/auth/auth.services';
import { RouterLink ,Router } from '@angular/router';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { BoxPasswordModal } from '../modal/box-password-modal/box-password-modal';
import { BoxServices } from '../../services/boxes/boxes';
import Notiflix from 'notiflix';
@Component({
  selector: 'app-dashboard',
  imports: [
    CommonModule,
    FormsModule,
    // RouterLink,
    // Router,
    NgxSkeletonLoaderModule,
    BoxPasswordModal
  ],
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.scss'
})
export class Dashboard {
  authService: AuthServices = inject(AuthServices);
  boxService: BoxServices = inject(BoxServices);
  searchTerm: string = '';
  loading = signal(true);
  isModalloading = signal(false);
  allBoxes: any[] = []
  // filteredData: any[] = [];
  filteredData = signal<any[]>([]);
  router: Router = inject(Router);

  isModalOpen = false;
  selectedBoxId = '';
  ngOnInit() {
    this.getAllBoxes()
  }

  getAllBoxes(){
    this.loading.set(true)
    this.searchTerm=''
    this.authService.getAllBoxes().subscribe({
      next:(res:any)=>{
        if(res.status == 'success' && res.data){
          this.allBoxes = res.data || [];
          this.filteredData.set(this.allBoxes);
          this.loading.set(false)
        }
      },
      error: (err) => {
        this.loading.set(false)
        console.error('getting error while loading the boxes', err);
        Notiflix.Notify.failure(err?.error.message || 'something went wrong');
        if(err.status === 401 ){
          this.authService.logout();
        }
      }
    })
  }

  getfilteredBoxes(){
    let data = this.allBoxes.filter(data =>
      data.box_id.toLowerCase().includes(this.searchTerm?.toLowerCase()?.trim())
      //     // data.box_id.toLowerCase().includes(term) ||
      //     // data.mac.toLowerCase().includes(term) ||
      //     // data.model.toLowerCase().includes(term) ||
      //     // data.status.toLowerCase().includes(term)
      );
    this.filteredData.set(data);
  }

  getBoxDeatils(box:any){
  console.log('box :', box);
  this.openModal(box.box_id);
  // this.router.navigate(['/box', box.box_id]);
  }



  openModal(id: string) {
    this.selectedBoxId = id;
    this.isModalOpen = true;
  }

  closeModal() {
    this.isModalOpen = false;
    this.selectedBoxId = '';
  }

  handlePasswordSubmit(event: { boxId: string, password: string }) {
    this.isModalloading.set(true)
    this.boxService.getBoxDetails(event.boxId, event.password).subscribe({
      next: (res: any) => {
      console.log('res :', res);
        this.boxService.boxDetails.set(res.data);
        this.router.navigate(['/box', event.boxId]);
        this.isModalloading.set(false)
        this.closeModal();
      },
      error: (error: any) => {
        this.isModalloading.set(false)
        if (error.status === 401) {
          console.error('Unauthorized: Invalid password');
          Notiflix.Notify.failure(error?.error.detail || 'Unauthorized: Invalid password',{
            position: 'center-top'
          });
        }
      }
    });    
  }

}
