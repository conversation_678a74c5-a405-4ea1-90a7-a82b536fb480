.page-not-found-container {
    text-align: center;
    padding: 80px 20px;
    color: #333;
    font-family: 'Segoe UI', sans-serif;
  
    .logo {
      width: 60px;
      margin-bottom: 20px;
    }
  
    .error-code {
      font-size: 96px;
      color: #4388D5;
      margin: 0;
    }
  
    .divider {
      width: 60px;
      height: 3px;
      background-color: #4388D5;
      margin: 10px auto 20px;
      border: none;
    }
  
    .error-text {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 10px;
    }
  
    .error-message {
      font-size: 16px;
      max-width: 400px;
      margin: 0 auto 30px;
      color: #555;
    }
  
    .primary-btn {
      background-color: #4388D5;
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      border-radius: 8px;
      margin-bottom: 20px;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
  
    .button-group {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 30px;
  
      .secondary-btn {
        background: white;
        border: 1px solid #ddd;
        padding: 10px 20px;
        font-size: 15px;
        border-radius: 6px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 6px;
      }
    }
  
    .support-text {
      font-size: 13px;
      color: #666;
  
      a {
        color: #4a5be5;
        text-decoration: none;
      }
    }
  }
  