import { Component ,inject} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthServices } from '../../services/auth/auth.services';
import { Router } from '@angular/router';
import Notiflix from 'notiflix';
@Component({
  selector: 'app-login',
  imports: [ReactiveFormsModule],
  templateUrl: './login.html',
  styleUrl: './login.scss'
})


export class Login {
  loginForm: FormGroup;
  showPassword = false;
  authService: AuthServices = inject(AuthServices);
  router: Router = inject(Router);

  constructor(    private fb: FormBuilder) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  get email() {
    return this.loginForm.get('email');
  }

  get password() {
    return this.loginForm.get('password');
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    if (this.loginForm.valid) {
      const { email, password } = this.loginForm.value;
  
      this.authService.login(email, password).subscribe({
        next: (res: any) => {
          if (res && res.access_token) {
            this.authService.setToken(res.email, res.access_token,res.first_name,res.last_name);
            Notiflix.Notify.success('Login successfuly');
            this.router.navigate(['/dashboard']);
          } else {
            Notiflix.Notify.failure('Invalid Username or Password!');
          }
        },
        error: (err) => {
          console.error('Login error:', err);
          Notiflix.Notify.failure(err?.error.detail || 'Login failed!');
        }
      });
  
    } else {
      this.loginForm.markAllAsTouched();
    }
  }
  
}
