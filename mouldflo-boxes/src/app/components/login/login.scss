.login-container {
    min-height: 98vh;
    background-color: #F3F4F6;
    display: flex;
    justify-content: center;
    align-items: center;
    // padding: 1rem;
    width: 100%;
  }
  
  .login-box {
    background: #fff;
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 350px;
  }
  
  .logo-section {
    text-align: center;
    margin-bottom: 2rem;
  
    h1 {
      font-size: 1.75rem;
      font-weight: bold;
      color: #2d3748;
  
      .highlight {
        color: #3182ce;
      }
    }
  
    p {
      color: #3182ce;
      font-weight: 500;
      font-size: 0.875rem;
    }
  }
  
  .welcome-text {
    text-align: center;
    margin-bottom: 1.5rem;
  
    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #2d3748;
    }
  
    p {
      font-size: 0.875rem;
      color: #718096;
    }
  }
  
  form {
    .form-group {
      position: relative;
      margin-bottom: 1.25rem;
  
      input {
        width: calc(100% - 2rem);
        padding: 0.75rem;
        border: 1px solid #cbd5e0;
        border-radius: 0.375rem;
        font-size: 1rem;
        outline: none;
  
        &.invalid {
          border-color: #e53e3e;
        }
  
        &:focus {
          border-color: #3182ce;
          box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.4);
        }
      }
  
      small {
        color: #e53e3e;
        font-size: 0.75rem;
        margin-top: 0.25rem;
        display: block;
      }
    }
  
    .password-group {
      .toggle-password {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
      }
    }
  
    .forgot-password {
      text-align: left;
      margin-bottom: 1rem;
  
      a {
        color: #3182ce;
        font-size: 0.875rem;
        text-decoration: none;
  
        &:hover {
          text-decoration: underline;
        }
      }
    }
  
    .submit-button {
      width: 100%;
      padding: 0.75rem;
      background-color: #3182ce;
      color: #fff;
      border: none;
      border-radius: 0.375rem;
      font-weight: 500;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.3s ease;
  
      &:hover {
        background-color: #2b6cb0;
      }
    }
  
    .signup-link {
      text-align: center;
      margin-top: 1.5rem;
      font-size: 0.875rem;
      color: #4a5568;
  
      a {
        color: #3182ce;
        font-weight: 500;
        text-decoration: none;
  
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  