import { inject } from '@angular/core';
import { Router, CanActivateFn } from '@angular/router';
import { AuthServices } from '../services/auth/auth.services';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthServices);
  const router = inject(Router);

  if (authService.isLoggedIn()) {
    return true;
  }

  // Redirect to login page if not authenticated
  router.navigate(['/login']);
  return false;
};