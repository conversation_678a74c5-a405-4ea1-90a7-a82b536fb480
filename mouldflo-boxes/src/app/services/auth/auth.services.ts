import { Injectable,Inject ,PLATFORM_ID,signal,inject} from '@angular/core';
import { Router } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import {environment} from '../../../environments/environment'
import { HttpClient,HttpHeaders } from '@angular/common/http';
import { Observable } from '../../../../node_modules/rxjs/dist/types/index';

@Injectable({
  providedIn: 'root'
})
export class AuthServices {
  private http = inject(HttpClient);
  public boxPassword = signal('');
  constructor(private router: Router , @Inject(PLATFORM_ID) private platformId: Object) {}



  //  AUTHENTICATION AND AUTHORIZATION
   public  setToken(email: string ,token:string ,firstName:string ,lastName:string): boolean {
    if (email) {
    const userData = { email, token ,firstName ,lastName};
    // Store in localStorage and cookies
    localStorage.setItem('user', JSON.stringify(userData));
    this.setCookie('auth_token', token, 1);
    return true;
  }else{
    return false;
  }}

  logout(): void {
    localStorage.removeItem('user');
    this.deleteCookie('auth_token');
    this.router.navigate(['/login']);
  }

  isLoggedIn(): boolean {
    if (isPlatformBrowser(this.platformId)) {
      return !!localStorage.getItem('user');
    }
    return false;
  }

  getToken(): string | null {
    const user = this.getUserData();
    return user ? user.token : null;
  }

  public getUserData(): any {
    const userData = localStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  }

  private setCookie(name: string, value: string, days: number): void {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const expires = `expires=${date.toUTCString()}`;
    document.cookie = `${name}=${value};${expires};path=/`;
  }

  private deleteCookie(name: string): void {
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
  }




  // API Calls 

  public login(email: string, password: string): Observable<any> {
    return this.http.post(environment?.apiUrl + 'login', { email, password });
  }
  public getAllBoxes(): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.getToken()}`
    });
    // For debugging purposes, let's add a tap operator to log the response
    return this.http.get(environment.apiUrl + 'boxes',{headers})
  }
  // public getBoxDetails(id: string ,password:string): Observable<any> {
  //   return this.http.post(environment.apiUrl + 'box_details/' + id , { id, password });
  // }
}
