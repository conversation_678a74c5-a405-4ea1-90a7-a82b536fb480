import { Injectable,Inject ,PLATFORM_ID,signal} from '@angular/core';
import { Router } from '@angular/router';
import {environment} from '../../../environments/environment'
import { HttpClient } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable } from '../../../../node_modules/rxjs/dist/types/index';
@Injectable({
  providedIn: 'root'
})
export class BoxServices {
  private http = inject(HttpClient);
  public boxDetails = signal(null);
  constructor(private router: Router) {}


  public getBoxDetails(id: string ,password:string): Observable<any> {
    return this.http.post(environment.apiUrl + 'box_details/' + id , { id, password });
  }


  // public boxDetailsData(boxId:any){

  // }
}
