import { Injectable,Inject ,PLATFORM_ID,signal} from '@angular/core';
import { Router } from '@angular/router';
import {environment} from '../../../environments/environment'
import { HttpClient,HttpHeaders } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable } from '../../../../node_modules/rxjs/dist/types/index';
import { AuthServices } from '../auth/auth.services';

@Injectable({
  providedIn: 'root'
})
export class BoxServices {
  private http = inject(HttpClient);
  public boxDetails = signal(null);
  authService: AuthServices = inject(AuthServices);
  constructor(private router: Router) {}


  public getBoxDetails(id: string ,password:string): Observable<any> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.authService.getToken()}`
    });
    return this.http.post(environment.apiUrl + 'box_details/' + id , { id, password } ,{headers});
  }

}
