import { Routes } from '@angular/router';
import { Login } from './components/login/login';
import { Dashboard } from './components/dashboard/dashboard';
import { LoggedinLayout } from './components/layouts/loggedin-layout/loggedin-layout';
import { PageNotFound } from './components/static-pages/page-not-found/page-not-found';
import { authGuard } from './guards/auth.guard';
import { BoxDetails } from './components/box-details/box-details';

export const routes: Routes = [
    {
        path: '', component: LoggedinLayout,
        canActivate: [authGuard],
        children: [
          {
            path: '',
            redirectTo: 'dashboard',
            pathMatch: 'full',
          },
          {
            path: 'dashboard',
            component: Dashboard,
          },
          {
            path: 'box/:id',
            component: BoxDetails,
          },
          
        ]
      },
      {
        path: 'login',
        component: Login,
      },
      {
        path: '**',
        component: PageNotFound,
      },
];
