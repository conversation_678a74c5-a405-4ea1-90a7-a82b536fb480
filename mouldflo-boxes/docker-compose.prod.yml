version: '3.8'

services:
  # Angular Production Build with Nginx
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    networks:
      - mouldflo-network
    restart: unless-stopped
    depends_on:
      - backend

  # Backend API (placeholder - replace with your actual backend)
  backend:
    image: python:3.9-alpine
    ports:
      - "8000:8000"
    command: >
      sh -c "
        echo 'Backend placeholder - replace with your actual backend service';
        python -m http.server 8000
      "
    networks:
      - mouldflo-network
    restart: unless-stopped

  # Optional: Database service
  # database:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: mouldflo
  #     POSTGRES_USER: mouldflo
  #     POSTGRES_PASSWORD: password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - mouldflo-network
  #   restart: unless-stopped

networks:
  mouldflo-network:
    driver: bridge

# volumes:
#   postgres_data:
