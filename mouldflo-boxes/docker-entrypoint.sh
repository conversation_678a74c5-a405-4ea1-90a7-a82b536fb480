#!/bin/sh

# Docker entrypoint script for runtime environment configuration

# Function to replace environment variables in built files
replace_env_vars() {
    echo "Replacing environment variables in built files..."
    
    # Replace API URL if provided
    if [ ! -z "$API_URL" ]; then
        echo "Setting API_URL to: $API_URL"
        find /usr/share/nginx/html -name "*.js" -exec sed -i "s|http://0.0.0.0:8000/|$API_URL|g" {} \;
    fi
    
    # Replace other environment variables as needed
    if [ ! -z "$PRODUCTION" ]; then
        echo "Setting production mode to: $PRODUCTION"
        find /usr/share/nginx/html -name "*.js" -exec sed -i "s|production:false|production:$PRODUCTION|g" {} \;
    fi
}

# Only replace environment variables if we're in the nginx container
if [ -d "/usr/share/nginx/html" ]; then
    replace_env_vars
fi

# Execute the main command
exec "$@"
