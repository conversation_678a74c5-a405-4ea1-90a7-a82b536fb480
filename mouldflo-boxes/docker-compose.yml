version: '3.8'

services:
  # Angular Development Server
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "4200:4200"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    networks:
      - mouldflo-network
    restart: unless-stopped

  # Backend API (placeholder - replace with your actual backend)
  backend:
    image: python:3.9-alpine
    ports:
      - "8000:8000"
    command: >
      sh -c "
        echo 'Backend placeholder - replace with your actual backend service';
        python -m http.server 8000
      "
    networks:
      - mouldflo-network
    restart: unless-stopped

networks:
  mouldflo-network:
    driver: bridge

volumes:
  node_modules:
