# Docker Setup for MouldFlo Boxes

This document explains how to run the MouldFlo Boxes Angular application using Docker.

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)

## Quick Start

### Development Environment

1. **Start the development environment:**
   ```bash
   docker-compose up -d
   ```

2. **Access the application:**
   - Frontend: http://localhost:4200
   - Backend API: http://localhost:8000

3. **View logs:**
   ```bash
   docker-compose logs -f frontend-dev
   ```

4. **Stop the development environment:**
   ```bash
   docker-compose down
   ```

### Production Environment

1. **Build and start production environment:**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. **Access the application:**
   - Frontend: http://localhost:80

3. **Stop the production environment:**
   ```bash
   docker-compose -f docker-compose.prod.yml down
   ```

## Docker Commands

### Building Images

**Development image:**
```bash
docker build -f Dockerfile.dev -t mouldflo-boxes:dev .
```

**Production image:**
```bash
docker build -t mouldflo-boxes:prod .
```

### Running Containers

**Development container:**
```bash
docker run -p 4200:4200 -v $(pwd):/app -v /app/node_modules mouldflo-boxes:dev
```

**Production container:**
```bash
docker run -p 80:80 mouldflo-boxes:prod
```

## Environment Variables

You can customize the application behavior using environment variables:

### Development
- `NODE_ENV`: Set to 'development'
- `CHOKIDAR_USEPOLLING`: Enable file watching in Docker (set to 'true')

### Production
- `API_URL`: Backend API URL (default: http://0.0.0.0:8000/)
- `PRODUCTION`: Set to 'true' for production mode

### Example with custom API URL:
```bash
docker run -p 80:80 -e API_URL=https://api.yourdomain.com/ mouldflo-boxes:prod
```

## File Structure

```
mouldflo-boxes/
├── Dockerfile              # Production multi-stage build
├── Dockerfile.dev          # Development environment
├── docker-compose.yml      # Development services
├── docker-compose.prod.yml # Production services
├── nginx.conf              # Nginx configuration for production
├── docker-entrypoint.sh    # Runtime configuration script
├── .dockerignore           # Files to exclude from Docker context
└── README.Docker.md        # This file
```

## Customization

### Backend Integration

Replace the placeholder backend service in `docker-compose.yml` and `docker-compose.prod.yml` with your actual backend service:

```yaml
backend:
  image: your-backend-image:latest
  ports:
    - "8000:8000"
  environment:
    - DATABASE_URL=************************************/dbname
  networks:
    - mouldflo-network
```

### Database Integration

Uncomment and configure the database service in `docker-compose.prod.yml`:

```yaml
database:
  image: postgres:15-alpine
  environment:
    POSTGRES_DB: mouldflo
    POSTGRES_USER: mouldflo
    POSTGRES_PASSWORD: your-secure-password
  volumes:
    - postgres_data:/var/lib/postgresql/data
  networks:
    - mouldflo-network
```

### SSL/HTTPS

For production with SSL, modify the nginx configuration or use a reverse proxy like Traefik or nginx-proxy.

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Check what's using the port
   lsof -i :4200
   # Kill the process or use a different port
   docker-compose up -d --scale frontend-dev=0
   ```

2. **Permission issues on Linux:**
   ```bash
   # Fix ownership of node_modules
   sudo chown -R $USER:$USER node_modules
   ```

3. **Hot reload not working:**
   - Ensure `CHOKIDAR_USEPOLLING=true` is set
   - Check that volumes are properly mounted

### Debugging

**Access container shell:**
```bash
# Development container
docker-compose exec frontend-dev sh

# Production container
docker run -it --entrypoint sh mouldflo-boxes:prod
```

**View container logs:**
```bash
docker-compose logs frontend-dev
```

**Inspect container:**
```bash
docker inspect mouldflo-boxes_frontend-dev_1
```

## Performance Optimization

### Production Build Optimization

1. **Multi-stage build** reduces final image size
2. **Nginx** serves static files efficiently
3. **Gzip compression** enabled for better performance
4. **Caching headers** set for static assets

### Development Optimization

1. **Volume mounting** for hot reload
2. **Node modules volume** prevents reinstallation
3. **Polling enabled** for file watching in Docker

## Security Considerations

1. **Non-root user** in production container
2. **Security headers** configured in Nginx
3. **Health checks** for container monitoring
4. **Minimal base images** (Alpine Linux)

## Monitoring

### Health Checks

Both development and production containers include health checks:

```bash
# Check container health
docker ps
# Look for "healthy" status
```

### Logs

```bash
# Follow logs
docker-compose logs -f

# View specific service logs
docker-compose logs frontend-dev
```
