#!/bin/bash

# Docker build script for MouldFlo Boxes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  dev         Build and run development environment"
    echo "  prod        Build and run production environment"
    echo "  build-dev   Build development image only"
    echo "  build-prod  Build production image only"
    echo "  clean       Clean up Docker images and containers"
    echo "  logs        Show logs from running containers"
    echo "  stop        Stop all running containers"
    echo "  help        Show this help message"
    echo ""
}

# Function to build development image
build_dev() {
    print_status "Building development image..."
    docker build -f Dockerfile.dev -t mouldflo-boxes:dev .
    print_status "Development image built successfully!"
}

# Function to build production image
build_prod() {
    print_status "Building production image..."
    docker build -t mouldflo-boxes:prod .
    print_status "Production image built successfully!"
}

# Function to run development environment
run_dev() {
    print_status "Starting development environment..."
    docker-compose up -d
    print_status "Development environment started!"
    print_status "Frontend available at: http://localhost:4200"
    print_status "Backend available at: http://localhost:8000"
    print_status "Use 'docker-compose logs -f' to view logs"
}

# Function to run production environment
run_prod() {
    print_status "Starting production environment..."
    docker-compose -f docker-compose.prod.yml up -d
    print_status "Production environment started!"
    print_status "Application available at: http://localhost:80"
    print_status "Use 'docker-compose -f docker-compose.prod.yml logs -f' to view logs"
}

# Function to clean up
clean_up() {
    print_status "Cleaning up Docker resources..."
    
    # Stop and remove containers
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # Remove images
    docker rmi mouldflo-boxes:dev 2>/dev/null || true
    docker rmi mouldflo-boxes:prod 2>/dev/null || true
    
    # Remove unused volumes and networks
    docker volume prune -f
    docker network prune -f
    
    print_status "Cleanup completed!"
}

# Function to show logs
show_logs() {
    if docker-compose ps | grep -q "Up"; then
        print_status "Showing development environment logs..."
        docker-compose logs -f
    elif docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        print_status "Showing production environment logs..."
        docker-compose -f docker-compose.prod.yml logs -f
    else
        print_warning "No running containers found!"
    fi
}

# Function to stop containers
stop_containers() {
    print_status "Stopping all containers..."
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    print_status "All containers stopped!"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Main script logic
case "${1:-help}" in
    "dev")
        build_dev
        run_dev
        ;;
    "prod")
        build_prod
        run_prod
        ;;
    "build-dev")
        build_dev
        ;;
    "build-prod")
        build_prod
        ;;
    "clean")
        clean_up
        ;;
    "logs")
        show_logs
        ;;
    "stop")
        stop_containers
        ;;
    "help"|*)
        show_usage
        ;;
esac
