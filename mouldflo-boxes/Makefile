# Makefile for MouldFlo Boxes Docker operations

.PHONY: help dev prod build-dev build-prod clean logs stop test

# Default target
help:
	@echo "Available commands:"
	@echo "  make dev        - Build and run development environment"
	@echo "  make prod       - Build and run production environment"
	@echo "  make build-dev  - Build development image only"
	@echo "  make build-prod - Build production image only"
	@echo "  make clean      - Clean up Docker images and containers"
	@echo "  make logs       - Show logs from running containers"
	@echo "  make stop       - Stop all running containers"
	@echo "  make test       - Run tests in Docker container"

# Development environment
dev: build-dev
	@echo "Starting development environment..."
	docker-compose up -d
	@echo "Development environment started!"
	@echo "Frontend: http://localhost:4200"
	@echo "Backend: http://localhost:8000"

# Production environment
prod: build-prod
	@echo "Starting production environment..."
	docker-compose -f docker-compose.prod.yml up -d
	@echo "Production environment started!"
	@echo "Application: http://localhost:80"

# Build development image
build-dev:
	@echo "Building development image..."
	docker build -f Dockerfile.dev -t mouldflo-boxes:dev .

# Build production image
build-prod:
	@echo "Building production image..."
	docker build -t mouldflo-boxes:prod .

# Clean up
clean:
	@echo "Cleaning up Docker resources..."
	-docker-compose down
	-docker-compose -f docker-compose.prod.yml down
	-docker rmi mouldflo-boxes:dev mouldflo-boxes:prod
	docker volume prune -f
	docker network prune -f
	@echo "Cleanup completed!"

# Show logs
logs:
	@if docker-compose ps | grep -q "Up"; then \
		echo "Showing development logs..."; \
		docker-compose logs -f; \
	elif docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then \
		echo "Showing production logs..."; \
		docker-compose -f docker-compose.prod.yml logs -f; \
	else \
		echo "No running containers found!"; \
	fi

# Stop containers
stop:
	@echo "Stopping all containers..."
	-docker-compose down
	-docker-compose -f docker-compose.prod.yml down
	@echo "All containers stopped!"

# Run tests
test:
	@echo "Running tests in Docker container..."
	docker run --rm -v $(PWD):/app -w /app node:20-alpine sh -c "npm install && npm test"
