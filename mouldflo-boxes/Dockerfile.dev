# Development Dockerfile
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install Angular CLI globally
RUN npm install -g @angular/cli@20

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# Expose port 4200 for Angular dev server
EXPOSE 4200

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:4200/ || exit 1

# Start the development server
CMD ["npm", "start", "--", "--host", "0.0.0.0", "--port", "4200"]
